# -*- coding: utf-8 -*-
"""
ProcessProxy功能测试模块
========================

该模块包含对 global_tools.utils.enhanced_process.ProcessProxy 功能的全面测试，
涵盖基础功能测试、集成测试、异常处理测试和性能测试。

测试模块结构:
- test_basic_functionality.py: 基础功能测试
- test_enhanced_process_integration.py: EnhancedProcess集成测试
- test_error_handling.py: 异常处理和边界条件测试
- test_performance.py: 性能和稳定性测试
- mock_services.py: 测试用服务类
- test_utils.py: 测试工具函数

核心测试目标:
1. 验证真实的跨进程方法调用（非拷贝调用）
2. 验证ProcessProxy与EnhancedProcess的完整集成
3. 验证多进程并发访问的数据一致性
4. 验证异常处理和错误恢复机制
5. 验证系统性能和长期稳定性

使用示例:
    ```python
    import unittest
    from test.test_process_proxy.test_basic_functionality import TestProcessProxyCore
    
    # 运行基础功能测试
    suite = unittest.TestLoader().loadTestsFromTestCase(TestProcessProxyCore)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    ```

作者：Augment Agent
版本：1.0.0
"""

__version__ = "1.0.0"
__author__ = "Augment Agent"

# 导入测试模块
from .mock_services import SimpleCounter, DataManager, StateTracker
from .test_utils import TestUtils, PerformanceMonitor

__all__ = [
    'SimpleCounter',
    'DataManager', 
    'StateTracker',
    'TestUtils',
    'PerformanceMonitor'
]
