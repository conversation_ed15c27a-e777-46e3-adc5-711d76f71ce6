# -*- coding: utf-8 -*-
"""
ProcessProxy异常处理和边界条件测试
=================================

该模块测试ProcessProxy的异常处理能力、边界条件和错误恢复机制，
确保系统在各种异常情况下的健壮性。

测试类说明:
- TestErrorHandling: 基础异常处理测试
- TestBoundaryConditions: 边界条件测试
- TestRecoveryMechanisms: 错误恢复机制测试

作者：Augment Agent
版本：1.0.0
"""

import unittest
import time
import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from global_tools.utils.enhanced_process import EnhancedProcess
from mock_services import SimpleCounter, DataManager, StateTracker
from test_utils import TestUtils


class TestErrorHandling(unittest.TestCase):
    """基础异常处理测试"""
    
    def setUp(self):
        """测试前准备"""
        self.counter = SimpleCounter()
        self.data_manager = DataManager()
        self.test_utils = TestUtils()
    
    def tearDown(self):
        """测试后清理"""
        self.counter.reset()
        self.data_manager.clear_all()
    
    def test_proxy_not_enabled_error(self):
        """测试未启用代理时的错误处理"""
        print("\n=== 测试未启用代理时的错误处理 ===")
        
        # 创建未启用代理的进程
        process = EnhancedProcess(
            target=lambda: None,
            enable_proxy=False
        )
        
        # 验证代理功能状态
        self.assertFalse(process.is_proxy_enabled(), "代理功能应该未启用")
        
        # 尝试注册对象应该抛出异常
        with self.assertRaises(RuntimeError) as context:
            process.register_proxy_object('test', self.counter)
        
        self.assertIn("ProcessProxy功能未启用", str(context.exception))
        print(f"正确捕获异常: {context.exception}")
        
        # 尝试注销对象应该抛出异常
        with self.assertRaises(RuntimeError) as context:
            process.unregister_proxy_object('test')
        
        self.assertIn("ProcessProxy功能未启用", str(context.exception))
        print(f"正确捕获异常: {context.exception}")
        
        # 获取代理管理器应该返回None
        manager = process.get_proxy_manager()
        self.assertIsNone(manager, "未启用代理时管理器应该为None")
        
        print("✓ 未启用代理时的错误处理测试通过")
    
    def test_invalid_object_registration(self):
        """测试无效对象注册"""
        print("\n=== 测试无效对象注册 ===")
        
        process = EnhancedProcess(
            target=lambda: None,
            enable_proxy=True
        )
        
        # 测试空ID
        with self.assertRaises(ValueError) as context:
            process.register_proxy_object('', self.counter)
        
        self.assertIn("obj_id 不能为空", str(context.exception))
        print(f"空ID异常: {context.exception}")
        
        # 测试None对象
        with self.assertRaises(ValueError) as context:
            process.register_proxy_object('test', None)
        
        self.assertIn("obj_instance 不能为None", str(context.exception))
        print(f"None对象异常: {context.exception}")
        
        # 测试重复注册
        process.register_proxy_object('test', self.counter)
        
        with self.assertRaises(ValueError) as context:
            process.register_proxy_object('test', self.counter)
        
        self.assertIn("对象ID 'test' 已存在", str(context.exception))
        print(f"重复注册异常: {context.exception}")
        
        # 测试注销不存在的对象
        with self.assertRaises(KeyError) as context:
            process.unregister_proxy_object('nonexistent')
        
        self.assertIn("对象ID 'nonexistent' 不存在", str(context.exception))
        print(f"不存在对象异常: {context.exception}")
        
        print("✓ 无效对象注册测试通过")
    
    def test_nonexistent_object_access(self):
        """测试访问不存在的代理对象"""
        print("\n=== 测试访问不存在的代理对象 ===")
        
        def worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
            """测试访问不存在对象的工作函数"""
            process_id = os.getpid()
            print(f"测试进程 {process_id} 开始执行")
            
            try:
                # 尝试访问不存在的对象
                nonexistent = proxy_accessor.get_proxy('nonexistent')
                return {'error': False, 'result': nonexistent}
            except Exception as e:
                print(f"捕获到预期异常: {type(e).__name__}: {e}")
                return {'error': True, 'exception_type': type(e).__name__, 'exception_message': str(e)}
        
        process = EnhancedProcess(
            target=worker,
            enable_proxy=True,
            proxy_objects={'counter': self.counter}
        )
        
        process.start()
        result = process.get_result(timeout=15)
        
        # 验证结果
        self.assertIsNotNone(result, "应该返回结果")
        self.assertTrue(result['error'], "应该捕获到异常")
        self.assertIn('exception_type', result, "应该包含异常类型")
        self.assertIn('exception_message', result, "应该包含异常消息")
        
        print(f"异常类型: {result['exception_type']}")
        print(f"异常消息: {result['exception_message']}")
        print("✓ 访问不存在代理对象测试通过")
    
    def test_process_timeout_handling(self):
        """测试进程超时处理"""
        print("\n=== 测试进程超时处理 ===")
        
        def slow_worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
            """慢速工作函数"""
            process_id = os.getpid()
            print(f"慢速进程 {process_id} 开始执行")
            
            counter = proxy_accessor.get_proxy('counter')
            
            # 模拟慢操作
            time.sleep(2.0)
            result = counter.increment()
            
            print(f"慢速进程 {process_id} 执行完成，结果: {result}")
            return result
        
        process = EnhancedProcess(
            target=slow_worker,
            enable_proxy=True,
            proxy_objects={'counter': self.counter}
        )
        
        process.start()
        
        # 短超时应该返回None
        print("测试短超时...")
        start_time = time.time()
        result_short = process.get_result(timeout=0.5)
        short_timeout_duration = time.time() - start_time
        
        self.assertIsNone(result_short, "短超时应该返回None")
        self.assertLess(short_timeout_duration, 1.0, "短超时应该快速返回")
        print(f"短超时正确返回None，耗时: {short_timeout_duration:.2f}秒")
        
        # 长超时应该成功
        print("测试长超时...")
        start_time = time.time()
        result_long = process.get_result(timeout=5.0)
        long_timeout_duration = time.time() - start_time
        
        self.assertIsNotNone(result_long, "长超时应该返回结果")
        self.assertEqual(result_long, 1, "结果应该正确")
        self.assertGreater(long_timeout_duration, 1.5, "应该等待足够时间")
        print(f"长超时成功返回结果: {result_long}，耗时: {long_timeout_duration:.2f}秒")
        
        # 验证主进程状态
        self.assertEqual(self.counter.get_value(), 1, "计数器应该被修改")
        
        print("✓ 进程超时处理测试通过")
    
    def test_proxy_method_exception_handling(self):
        """测试代理方法异常处理"""
        print("\n=== 测试代理方法异常处理 ===")
        
        class ExceptionService:
            """会抛出异常的测试服务"""
            
            def __init__(self):
                self.call_count = 0
            
            def normal_method(self):
                """正常方法"""
                self.call_count += 1
                return f"success_{self.call_count}"
            
            def exception_method(self):
                """会抛出异常的方法"""
                self.call_count += 1
                raise ValueError(f"测试异常_{self.call_count}")
            
            def get_call_count(self):
                """获取调用次数"""
                return self.call_count
        
        exception_service = ExceptionService()
        
        def worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
            """测试异常处理的工作函数"""
            process_id = os.getpid()
            print(f"异常测试进程 {process_id} 开始执行")
            
            service = proxy_accessor.get_proxy('exception_service')
            results = []
            
            # 调用正常方法
            try:
                result1 = service.normal_method()
                results.append({'method': 'normal_method', 'success': True, 'result': result1})
                print(f"正常方法调用成功: {result1}")
            except Exception as e:
                results.append({'method': 'normal_method', 'success': False, 'error': str(e)})
            
            # 调用异常方法
            try:
                result2 = service.exception_method()
                results.append({'method': 'exception_method', 'success': True, 'result': result2})
            except Exception as e:
                results.append({'method': 'exception_method', 'success': False, 'error': str(e)})
                print(f"异常方法正确抛出异常: {e}")
            
            # 再次调用正常方法，验证服务仍然可用
            try:
                result3 = service.normal_method()
                results.append({'method': 'normal_method_after_exception', 'success': True, 'result': result3})
                print(f"异常后正常方法调用成功: {result3}")
            except Exception as e:
                results.append({'method': 'normal_method_after_exception', 'success': False, 'error': str(e)})
            
            final_count = service.get_call_count()
            
            print(f"异常测试进程 {process_id} 执行完成")
            return {'results': results, 'final_count': final_count}
        
        process = EnhancedProcess(
            target=worker,
            enable_proxy=True,
            proxy_objects={'exception_service': exception_service}
        )
        
        process.start()
        result = process.get_result(timeout=15)
        
        # 验证结果
        self.assertIsNotNone(result, "应该返回结果")
        self.assertEqual(len(result['results']), 3, "应该有3个方法调用结果")
        
        # 验证第一次正常调用
        self.assertTrue(result['results'][0]['success'], "第一次正常调用应该成功")
        self.assertEqual(result['results'][0]['result'], 'success_1', "第一次调用结果应该正确")
        
        # 验证异常调用
        self.assertFalse(result['results'][1]['success'], "异常方法应该失败")
        self.assertIn('测试异常_2', result['results'][1]['error'], "应该包含预期异常信息")
        
        # 验证异常后的正常调用
        self.assertTrue(result['results'][2]['success'], "异常后正常调用应该成功")
        self.assertEqual(result['results'][2]['result'], 'success_3', "异常后调用结果应该正确")
        
        # 验证调用计数
        self.assertEqual(result['final_count'], 3, "应该有3次方法调用")
        self.assertEqual(exception_service.get_call_count(), 3, "主进程服务应该记录3次调用")
        
        print("✓ 代理方法异常处理测试通过")
    
    def test_concurrent_error_scenarios(self):
        """测试并发错误场景"""
        print("\n=== 测试并发错误场景 ===")
        
        def error_prone_worker(shared_data_proxy, data_queue, process_logger, proxy_accessor, worker_id):
            """容易出错的工作函数"""
            process_id = os.getpid()
            print(f"错误测试进程 {worker_id} (PID: {process_id}) 开始执行")
            
            counter = proxy_accessor.get_proxy('counter')
            results = []
            errors = []
            
            for i in range(5):
                try:
                    # 模拟一些操作可能失败
                    if worker_id == 1 and i == 2:
                        # 模拟特定进程在特定步骤出错
                        raise RuntimeError(f"模拟错误: worker_{worker_id}_step_{i}")
                    
                    count = counter.increment()
                    results.append(count)
                    time.sleep(0.01)  # 短暂延迟
                    
                except Exception as e:
                    errors.append({'step': i, 'error': str(e)})
                    print(f"进程 {worker_id} 步骤 {i} 出错: {e}")
            
            print(f"错误测试进程 {worker_id} 执行完成")
            return {
                'worker_id': worker_id,
                'process_id': process_id,
                'results': results,
                'errors': errors,
                'final_count': counter.get_value()
            }
        
        # 创建多个进程，其中一个会出错
        processes = []
        for i in range(3):
            process = EnhancedProcess(
                target=error_prone_worker,
                enable_proxy=True,
                proxy_objects={'counter': self.counter}
            )
            processes.append(process)
        
        # 启动所有进程
        for i, process in enumerate(processes):
            process.start(worker_id=i)
        
        # 收集结果
        results = []
        for i, process in enumerate(processes):
            result = process.get_result(timeout=20)
            self.assertIsNotNone(result, f"进程 {i} 应该返回结果")
            results.append(result)
        
        # 验证结果
        total_successful_operations = 0
        total_errors = 0
        
        for result in results:
            total_successful_operations += len(result['results'])
            total_errors += len(result['errors'])
            
            if result['worker_id'] == 1:
                # 进程1应该有1个错误
                self.assertEqual(len(result['errors']), 1, "进程1应该有1个错误")
                self.assertEqual(len(result['results']), 4, "进程1应该有4个成功操作")
            else:
                # 其他进程应该没有错误
                self.assertEqual(len(result['errors']), 0, f"进程{result['worker_id']}应该没有错误")
                self.assertEqual(len(result['results']), 5, f"进程{result['worker_id']}应该有5个成功操作")
        
        # 验证总体状态
        expected_operations = 14  # 3个进程 × 5次操作 - 1次错误 = 14次成功
        self.assertEqual(total_successful_operations, expected_operations, "总成功操作数应该正确")
        self.assertEqual(total_errors, 1, "总错误数应该是1")
        self.assertEqual(self.counter.get_value(), expected_operations, "计数器应该反映成功操作数")
        
        print(f"总成功操作: {total_successful_operations}, 总错误: {total_errors}")
        print("✓ 并发错误场景测试通过")


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
