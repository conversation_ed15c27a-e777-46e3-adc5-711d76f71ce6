# -*- coding: utf-8 -*-
"""
ProcessProxy测试工具函数
=======================

该模块提供用于ProcessProxy测试的各种工具函数和辅助类，
包括性能监控、结果验证、测试数据生成等功能。

工具类说明:
- TestUtils: 通用测试工具函数
- PerformanceMonitor: 性能监控和基准测试
- ResultValidator: 测试结果验证器

作者：Augment Agent
版本：1.0.0
"""

import time
import statistics
import threading
import multiprocessing
from typing import Any, Dict, List, Optional, Callable
from contextlib import contextmanager


class TestUtils:
    """
    通用测试工具函数
    
    该类提供各种测试辅助功能，包括进程管理、结果验证、
    数据生成和测试环境设置等。
    
    使用示例：
        utils = TestUtils()
        result = utils.wait_for_condition(lambda: counter.get_value() > 0, timeout=5)
        utils.verify_cross_process_call(original_state, modified_state)
    """
    
    @staticmethod
    def wait_for_condition(condition: Callable[[], bool], timeout: float = 10.0, interval: float = 0.1):
        """
        等待条件满足
        
        Args:
            condition (Callable): 条件检查函数
            timeout (float): 超时时间（秒）
            interval (float): 检查间隔（秒）
            
        Returns:
            bool: 条件是否在超时前满足
            
        使用示例：
            success = TestUtils.wait_for_condition(
                lambda: counter.get_value() > 5,
                timeout=10.0
            )
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            if condition():
                return True
            time.sleep(interval)
        return False
    
    @staticmethod
    def verify_cross_process_call(original_state: Any, modified_state: Any, expected_change: Any = None):
        """
        验证跨进程调用的真实性
        
        Args:
            original_state (Any): 原始状态
            modified_state (Any): 修改后状态
            expected_change (Any, optional): 预期变化
            
        Returns:
            Dict: 验证结果
            
        使用示例：
            result = TestUtils.verify_cross_process_call(
                original_count, 
                final_count, 
                expected_change=5
            )
            assert result['is_real_call']
        """
        is_changed = original_state != modified_state
        change_matches_expected = True
        
        if expected_change is not None:
            if isinstance(original_state, (int, float)):
                actual_change = modified_state - original_state
                change_matches_expected = actual_change == expected_change
            else:
                change_matches_expected = modified_state == expected_change
        
        return {
            'is_real_call': is_changed,
            'original_state': original_state,
            'modified_state': modified_state,
            'change_matches_expected': change_matches_expected,
            'verification_time': time.time()
        }
    
    @staticmethod
    def generate_test_data(count: int, data_type: str = 'mixed'):
        """
        生成测试数据
        
        Args:
            count (int): 数据条目数量
            data_type (str): 数据类型 ('string', 'number', 'dict', 'mixed')
            
        Returns:
            List: 测试数据列表
            
        使用示例：
            test_data = TestUtils.generate_test_data(100, 'mixed')
            for item in test_data:
                data_manager.store(f"key_{item['id']}", item['value'])
        """
        data = []
        for i in range(count):
            if data_type == 'string':
                data.append(f"test_string_{i}")
            elif data_type == 'number':
                data.append(i * 1.5)
            elif data_type == 'dict':
                data.append({
                    'id': i,
                    'name': f"item_{i}",
                    'value': i * 2,
                    'timestamp': time.time()
                })
            else:  # mixed
                if i % 3 == 0:
                    data.append(f"string_{i}")
                elif i % 3 == 1:
                    data.append(i)
                else:
                    data.append({'id': i, 'data': f"mixed_{i}"})
        return data
    
    @staticmethod
    def create_worker_function(operations: List[str], service_names: List[str]):
        """
        动态创建工作函数
        
        Args:
            operations (List[str]): 操作列表
            service_names (List[str]): 服务名称列表
            
        Returns:
            Callable: 工作函数
            
        使用示例：
            worker = TestUtils.create_worker_function(
                ['increment', 'store'], 
                ['counter', 'data']
            )
        """
        def worker(shared_data_proxy, data_queue, process_logger, proxy_accessor, **kwargs):
            results = {}
            process_id = multiprocessing.current_process().pid
            
            for service_name in service_names:
                service = proxy_accessor.get_proxy(service_name)
                service_results = []
                
                for operation in operations:
                    if hasattr(service, operation):
                        if operation == 'increment':
                            result = service.increment()
                        elif operation == 'store':
                            key = f"proc_{process_id}_op_{len(service_results)}"
                            result = service.store(key, f"value_{time.time()}")
                        elif operation == 'change_state':
                            result = service.change_state(f"state_{len(service_results)}", process_id)
                        else:
                            result = getattr(service, operation)()
                        service_results.append(result)
                
                results[service_name] = service_results
            
            return {
                'process_id': process_id,
                'results': results,
                'timestamp': time.time()
            }
        
        return worker
    
    @staticmethod
    def compare_performance(baseline_time: float, test_time: float, tolerance: float = 0.2):
        """
        比较性能结果
        
        Args:
            baseline_time (float): 基准时间
            test_time (float): 测试时间
            tolerance (float): 容忍度（百分比）
            
        Returns:
            Dict: 性能比较结果
            
        使用示例：
            comparison = TestUtils.compare_performance(1.0, 1.1, 0.15)
            assert comparison['within_tolerance']
        """
        if baseline_time == 0:
            return {
                'performance_ratio': float('inf') if test_time > 0 else 1.0,
                'within_tolerance': test_time == 0,
                'baseline_time': baseline_time,
                'test_time': test_time
            }
        
        ratio = test_time / baseline_time
        within_tolerance = abs(ratio - 1.0) <= tolerance
        
        return {
            'performance_ratio': ratio,
            'within_tolerance': within_tolerance,
            'baseline_time': baseline_time,
            'test_time': test_time,
            'tolerance': tolerance,
            'performance_change': (ratio - 1.0) * 100  # 百分比变化
        }


class PerformanceMonitor:
    """
    性能监控和基准测试
    
    该类提供性能监控、基准测试和性能分析功能，
    用于测试ProcessProxy的性能表现。
    
    使用示例：
        monitor = PerformanceMonitor()
        with monitor.measure('proxy_call'):
            result = proxy.some_method()
        stats = monitor.get_statistics('proxy_call')
    """
    
    def __init__(self):
        """初始化性能监控器"""
        self.measurements = {}
        self._lock = threading.Lock()
    
    @contextmanager
    def measure(self, operation_name: str):
        """
        测量操作执行时间
        
        Args:
            operation_name (str): 操作名称
            
        使用示例：
            with monitor.measure('database_query'):
                result = database.query('SELECT * FROM users')
        """
        start_time = time.time()
        try:
            yield
        finally:
            end_time = time.time()
            duration = end_time - start_time
            self.record_measurement(operation_name, duration)
    
    def record_measurement(self, operation_name: str, duration: float):
        """
        记录测量结果
        
        Args:
            operation_name (str): 操作名称
            duration (float): 执行时间（秒）
            
        使用示例：
            monitor.record_measurement('proxy_call', 0.05)
        """
        with self._lock:
            if operation_name not in self.measurements:
                self.measurements[operation_name] = []
            self.measurements[operation_name].append({
                'duration': duration,
                'timestamp': time.time()
            })
    
    def get_statistics(self, operation_name: str):
        """
        获取操作统计信息
        
        Args:
            operation_name (str): 操作名称
            
        Returns:
            Dict: 统计信息
            
        使用示例：
            stats = monitor.get_statistics('proxy_call')
            print(f"平均时间: {stats['mean']:.3f}秒")
        """
        with self._lock:
            if operation_name not in self.measurements:
                return None
            
            durations = [m['duration'] for m in self.measurements[operation_name]]
            
            if not durations:
                return None
            
            return {
                'count': len(durations),
                'mean': statistics.mean(durations),
                'median': statistics.median(durations),
                'min': min(durations),
                'max': max(durations),
                'stdev': statistics.stdev(durations) if len(durations) > 1 else 0,
                'total_time': sum(durations),
                'operations_per_second': len(durations) / sum(durations) if sum(durations) > 0 else 0
            }
    
    def get_all_statistics(self):
        """
        获取所有操作的统计信息
        
        Returns:
            Dict: 所有操作的统计信息
            
        使用示例：
            all_stats = monitor.get_all_statistics()
            for op_name, stats in all_stats.items():
                print(f"{op_name}: {stats['mean']:.3f}秒")
        """
        with self._lock:
            return {name: self.get_statistics(name) for name in self.measurements.keys()}
    
    def reset(self, operation_name: Optional[str] = None):
        """
        重置测量数据
        
        Args:
            operation_name (str, optional): 要重置的操作名称，None表示重置所有
            
        使用示例：
            monitor.reset('proxy_call')  # 重置特定操作
            monitor.reset()              # 重置所有操作
        """
        with self._lock:
            if operation_name:
                if operation_name in self.measurements:
                    self.measurements[operation_name].clear()
            else:
                self.measurements.clear()
    
    def create_performance_report(self):
        """
        创建性能报告
        
        Returns:
            str: 格式化的性能报告
            
        使用示例：
            report = monitor.create_performance_report()
            print(report)
        """
        all_stats = self.get_all_statistics()
        
        if not all_stats:
            return "没有性能数据可用"
        
        report_lines = ["性能监控报告", "=" * 50]
        
        for operation_name, stats in all_stats.items():
            if stats:
                report_lines.extend([
                    f"\n操作: {operation_name}",
                    f"  调用次数: {stats['count']}",
                    f"  平均时间: {stats['mean']:.4f}秒",
                    f"  中位数时间: {stats['median']:.4f}秒",
                    f"  最小时间: {stats['min']:.4f}秒",
                    f"  最大时间: {stats['max']:.4f}秒",
                    f"  标准差: {stats['stdev']:.4f}秒",
                    f"  总时间: {stats['total_time']:.4f}秒",
                    f"  操作/秒: {stats['operations_per_second']:.2f}"
                ])
        
        return "\n".join(report_lines)
