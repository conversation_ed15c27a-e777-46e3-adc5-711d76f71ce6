# -*- coding: utf-8 -*-
"""
ProcessProxy测试用服务类
=======================

该模块提供用于测试ProcessProxy功能的各种服务类，
涵盖不同复杂度和使用场景的测试需求。

服务类说明:
- SimpleCounter: 简单计数器服务，用于验证基础代理功能
- DataManager: 数据管理服务，用于验证复杂交互和状态管理
- StateTracker: 状态追踪服务，用于验证真实性和进程间通信

作者：Augment Agent
版本：1.0.0
"""

import time
import threading
from typing import Any, Dict, List, Optional


class SimpleCounter:
    """
    简单计数器服务 - 验证基础代理功能
    
    该类提供基础的计数功能，用于测试ProcessProxy的基本方法调用、
    状态同步和跨进程访问能力。
    
    功能特性：
    - 基础计数操作：increment、get_value、reset
    - 操作历史记录：记录所有操作的详细历史
    - 线程安全：支持多进程并发访问
    
    使用示例：
        counter = SimpleCounter()
        result = counter.increment()  # 返回1
        value = counter.get_value()   # 返回1
        history = counter.get_call_history()  # 获取操作历史
    """
    
    def __init__(self):
        """初始化计数器服务"""
        self.count = 0
        self.call_history = []
        self._lock = threading.Lock()
        self.creation_time = time.time()
    
    def increment(self):
        """
        递增计数器
        
        Returns:
            int: 递增后的计数值
            
        使用示例：
            result = counter.increment()
            print(f"当前计数: {result}")
        """
        with self._lock:
            self.count += 1
            self.call_history.append({
                'operation': 'increment',
                'result': self.count,
                'timestamp': time.time()
            })
            return self.count
    
    def get_value(self):
        """
        获取当前计数值
        
        Returns:
            int: 当前计数值
            
        使用示例：
            value = counter.get_value()
            print(f"当前值: {value}")
        """
        with self._lock:
            return self.count
    
    def reset(self):
        """
        重置计数器
        
        Returns:
            int: 重置前的计数值
            
        使用示例：
            old_value = counter.reset()
            print(f"已重置，原值: {old_value}")
        """
        with self._lock:
            old_count = self.count
            self.count = 0
            self.call_history.append({
                'operation': 'reset',
                'old_value': old_count,
                'new_value': 0,
                'timestamp': time.time()
            })
            return old_count
    
    def get_call_history(self):
        """
        获取操作历史记录
        
        Returns:
            List[Dict]: 操作历史列表
            
        使用示例：
            history = counter.get_call_history()
            for entry in history:
                print(f"{entry['operation']}: {entry['result']}")
        """
        with self._lock:
            return self.call_history.copy()
    
    def get_stats(self):
        """
        获取统计信息
        
        Returns:
            Dict: 包含各种统计信息的字典
            
        使用示例：
            stats = counter.get_stats()
            print(f"总操作数: {stats['total_operations']}")
        """
        with self._lock:
            return {
                'current_value': self.count,
                'total_operations': len(self.call_history),
                'creation_time': self.creation_time,
                'uptime': time.time() - self.creation_time
            }


class DataManager:
    """
    数据管理服务 - 验证复杂交互和状态管理
    
    该类提供数据存储和管理功能，用于测试ProcessProxy的复杂对象操作、
    数据一致性和并发访问能力。
    
    功能特性：
    - 数据存储：store、retrieve、delete操作
    - 数据查询：list_keys、search、get_stats
    - 访问日志：详细记录所有数据访问操作
    - 并发安全：支持多进程并发数据操作
    
    使用示例：
        data_mgr = DataManager()
        data_mgr.store('key1', 'value1')
        value = data_mgr.retrieve('key1')
        keys = data_mgr.list_keys()
    """
    
    def __init__(self):
        """初始化数据管理服务"""
        self.data = {}
        self.access_log = []
        self.operation_count = 0
        self._lock = threading.Lock()
        self.creation_time = time.time()
    
    def store(self, key: str, value: Any):
        """
        存储数据
        
        Args:
            key (str): 数据键
            value (Any): 数据值
            
        Returns:
            bool: 存储是否成功
            
        使用示例：
            success = data_mgr.store('user_1', {'name': 'Alice', 'age': 30})
            print(f"存储结果: {success}")
        """
        with self._lock:
            self.data[key] = value
            self.operation_count += 1
            self.access_log.append({
                'operation': 'store',
                'key': key,
                'value_type': type(value).__name__,
                'timestamp': time.time()
            })
            return True
    
    def retrieve(self, key: str):
        """
        检索数据
        
        Args:
            key (str): 数据键
            
        Returns:
            Any: 数据值，如果不存在则返回None
            
        使用示例：
            value = data_mgr.retrieve('user_1')
            if value:
                print(f"用户信息: {value}")
        """
        with self._lock:
            self.operation_count += 1
            value = self.data.get(key)
            self.access_log.append({
                'operation': 'retrieve',
                'key': key,
                'found': value is not None,
                'timestamp': time.time()
            })
            return value
    
    def delete(self, key: str):
        """
        删除数据
        
        Args:
            key (str): 要删除的数据键
            
        Returns:
            bool: 删除是否成功
            
        使用示例：
            success = data_mgr.delete('user_1')
            print(f"删除结果: {success}")
        """
        with self._lock:
            if key in self.data:
                del self.data[key]
                self.operation_count += 1
                self.access_log.append({
                    'operation': 'delete',
                    'key': key,
                    'success': True,
                    'timestamp': time.time()
                })
                return True
            else:
                self.access_log.append({
                    'operation': 'delete',
                    'key': key,
                    'success': False,
                    'timestamp': time.time()
                })
                return False
    
    def list_keys(self):
        """
        列出所有数据键
        
        Returns:
            List[str]: 数据键列表
            
        使用示例：
            keys = data_mgr.list_keys()
            print(f"所有键: {keys}")
        """
        with self._lock:
            keys = list(self.data.keys())
            self.access_log.append({
                'operation': 'list_keys',
                'count': len(keys),
                'timestamp': time.time()
            })
            return keys
    
    def search(self, pattern: str):
        """
        搜索匹配模式的键
        
        Args:
            pattern (str): 搜索模式
            
        Returns:
            List[str]: 匹配的键列表
            
        使用示例：
            matches = data_mgr.search('user_')
            print(f"匹配的键: {matches}")
        """
        with self._lock:
            matches = [key for key in self.data.keys() if pattern in key]
            self.access_log.append({
                'operation': 'search',
                'pattern': pattern,
                'matches': len(matches),
                'timestamp': time.time()
            })
            return matches
    
    def get_stats(self):
        """
        获取统计信息
        
        Returns:
            Dict: 包含各种统计信息的字典
            
        使用示例：
            stats = data_mgr.get_stats()
            print(f"数据条目数: {stats['data_count']}")
            print(f"总操作数: {stats['total_operations']}")
        """
        with self._lock:
            return {
                'data_count': len(self.data),
                'total_operations': self.operation_count,
                'log_entries': len(self.access_log),
                'creation_time': self.creation_time,
                'uptime': time.time() - self.creation_time
            }
    
    def get_access_log(self):
        """
        获取访问日志
        
        Returns:
            List[Dict]: 访问日志列表
            
        使用示例：
            log = data_mgr.get_access_log()
            for entry in log[-5:]:  # 显示最近5条
                print(f"{entry['operation']}: {entry.get('key', 'N/A')}")
        """
        with self._lock:
            return self.access_log.copy()
    
    def clear_all(self):
        """
        清空所有数据
        
        Returns:
            int: 清空前的数据条目数
            
        使用示例：
            count = data_mgr.clear_all()
            print(f"已清空 {count} 条数据")
        """
        with self._lock:
            count = len(self.data)
            self.data.clear()
            self.operation_count += 1
            self.access_log.append({
                'operation': 'clear_all',
                'cleared_count': count,
                'timestamp': time.time()
            })
            return count


class StateTracker:
    """
    状态追踪服务 - 验证真实性和进程间通信

    该类提供状态追踪和进程监控功能，用于测试ProcessProxy的真实性验证、
    进程间状态同步和并发访问追踪。

    功能特性：
    - 状态管理：change_state、get_current_state
    - 进程追踪：记录不同进程的调用情况
    - 转换历史：详细记录所有状态转换
    - 统计分析：提供进程调用统计和分析

    使用示例：
        tracker = StateTracker()
        tracker.change_state('processing', process_id=12345)
        state = tracker.get_current_state()
        history = tracker.get_transition_history()
    """

    def __init__(self):
        """初始化状态追踪服务"""
        self.state = "initial"
        self.transitions = []
        self.process_calls = {}
        self._lock = threading.Lock()
        self.creation_time = time.time()

    def change_state(self, new_state: str, process_id: Optional[int] = None):
        """
        改变当前状态

        Args:
            new_state (str): 新状态
            process_id (int, optional): 进程ID

        Returns:
            str: 状态转换描述

        使用示例：
            result = tracker.change_state('processing', process_id=os.getpid())
            print(f"状态转换: {result}")
        """
        with self._lock:
            old_state = self.state
            self.state = new_state
            transition = {
                'from_state': old_state,
                'to_state': new_state,
                'process_id': process_id,
                'timestamp': time.time()
            }
            self.transitions.append(transition)

            if process_id:
                if process_id not in self.process_calls:
                    self.process_calls[process_id] = 0
                self.process_calls[process_id] += 1

            return f"{old_state} -> {new_state}"

    def get_current_state(self):
        """
        获取当前状态

        Returns:
            str: 当前状态

        使用示例：
            state = tracker.get_current_state()
            print(f"当前状态: {state}")
        """
        with self._lock:
            return self.state

    def get_transition_history(self):
        """
        获取状态转换历史

        Returns:
            List[Dict]: 转换历史列表

        使用示例：
            history = tracker.get_transition_history()
            for transition in history:
                print(f"{transition['from_state']} -> {transition['to_state']}")
        """
        with self._lock:
            return self.transitions.copy()

    def get_process_stats(self):
        """
        获取进程调用统计

        Returns:
            Dict[int, int]: 进程ID到调用次数的映射

        使用示例：
            stats = tracker.get_process_stats()
            for pid, count in stats.items():
                print(f"进程 {pid}: {count} 次调用")
        """
        with self._lock:
            return self.process_calls.copy()

    def get_unique_processes(self):
        """
        获取参与过状态变更的唯一进程数量

        Returns:
            int: 唯一进程数量

        使用示例：
            count = tracker.get_unique_processes()
            print(f"参与进程数: {count}")
        """
        with self._lock:
            return len(self.process_calls)

    def reset_state(self, new_initial_state: str = "initial"):
        """
        重置状态追踪器

        Args:
            new_initial_state (str): 新的初始状态

        Returns:
            Dict: 重置前的统计信息

        使用示例：
            old_stats = tracker.reset_state('ready')
            print(f"重置前有 {old_stats['total_transitions']} 次转换")
        """
        with self._lock:
            old_stats = {
                'old_state': self.state,
                'total_transitions': len(self.transitions),
                'unique_processes': len(self.process_calls),
                'total_calls': sum(self.process_calls.values())
            }

            self.state = new_initial_state
            self.transitions.clear()
            self.process_calls.clear()

            # 记录重置操作
            self.transitions.append({
                'from_state': old_stats['old_state'],
                'to_state': new_initial_state,
                'process_id': None,
                'timestamp': time.time(),
                'operation': 'reset'
            })

            return old_stats

    def get_comprehensive_stats(self):
        """
        获取综合统计信息

        Returns:
            Dict: 包含所有统计信息的字典

        使用示例：
            stats = tracker.get_comprehensive_stats()
            print(f"运行时间: {stats['uptime']:.2f}秒")
            print(f"平均转换间隔: {stats['avg_transition_interval']:.2f}秒")
        """
        with self._lock:
            total_transitions = len(self.transitions)
            uptime = time.time() - self.creation_time

            # 计算平均转换间隔
            avg_interval = 0
            if total_transitions > 1:
                time_span = self.transitions[-1]['timestamp'] - self.transitions[0]['timestamp']
                avg_interval = time_span / (total_transitions - 1)

            return {
                'current_state': self.state,
                'total_transitions': total_transitions,
                'unique_processes': len(self.process_calls),
                'total_process_calls': sum(self.process_calls.values()),
                'creation_time': self.creation_time,
                'uptime': uptime,
                'avg_transition_interval': avg_interval,
                'most_active_process': max(self.process_calls.items(), key=lambda x: x[1]) if self.process_calls else None
            }
