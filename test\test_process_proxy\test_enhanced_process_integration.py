# -*- coding: utf-8 -*-
"""
EnhancedProcess与ProcessProxy集成测试
===================================

该模块测试EnhancedProcess与ProcessProxy的完整集成功能，
包括手动配置、装饰器配置、函数签名检测等。

测试类说明:
- TestEnhancedProcessIntegration: EnhancedProcess集成测试
- TestDecoratorSupport: 装饰器功能测试
- TestSignatureDetection: 函数签名检测测试

作者：Augment Agent
版本：1.0.0
"""

import unittest
import time
import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from global_tools.utils.enhanced_process import (
    EnhancedProcess, with_proxy_objects, process_proxy_enabled, 
    extract_proxy_config, ProcessProxyContext
)
from mock_services import SimpleCounter, DataManager, StateTracker
from test_utils import TestUtils, PerformanceMonitor


class TestEnhancedProcessIntegration(unittest.TestCase):
    """EnhancedProcess与ProcessProxy集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.counter = SimpleCounter()
        self.data_manager = DataManager()
        self.state_tracker = StateTracker()
        self.test_utils = TestUtils()
    
    def tearDown(self):
        """测试后清理"""
        self.counter.reset()
        self.data_manager.clear_all()
        self.state_tracker.reset_state()
    
    def test_manual_proxy_configuration(self):
        """测试手动配置代理功能"""
        print("\n=== 测试手动配置代理功能 ===")
        
        def worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
            """手动配置的工作函数"""
            process_id = os.getpid()
            print(f"手动配置工作进程 {process_id} 开始执行")
            
            counter = proxy_accessor.get_proxy('counter')
            data = proxy_accessor.get_proxy('data')
            tracker = proxy_accessor.get_proxy('tracker')
            
            # 执行操作
            count1 = counter.increment()
            count2 = counter.increment()
            data.store('test_key', f'count_{count2}')
            tracker.change_state('manual_config_test', process_id)
            
            print(f"手动配置工作进程 {process_id} 执行完成")
            
            return {
                'process_id': process_id,
                'counts': [count1, count2],
                'stored_value': data.retrieve('test_key'),
                'final_state': tracker.get_current_state()
            }
        
        # 手动配置
        process = EnhancedProcess(
            target=worker,
            enable_proxy=True,
            proxy_objects={
                'counter': self.counter,
                'data': self.data_manager,
                'tracker': self.state_tracker
            }
        )
        
        # 验证配置
        self.assertTrue(process.is_proxy_enabled(), "代理功能应该被启用")
        registered = process.get_registered_proxy_objects()
        self.assertEqual(set(registered), {'counter', 'data', 'tracker'}, "应该注册所有指定对象")
        
        print(f"已注册对象: {registered}")
        
        # 执行测试
        process.start()
        result = process.get_result(timeout=15)
        
        # 验证结果
        self.assertIsNotNone(result, "应该返回结果")
        self.assertEqual(result['counts'], [1, 2], "计数结果应该正确")
        self.assertEqual(result['stored_value'], 'count_2', "存储的值应该正确")
        self.assertEqual(result['final_state'], 'manual_config_test', "状态应该被正确设置")
        
        # 验证主进程状态
        self.assertEqual(self.counter.get_value(), 2, "主进程计数器应该被修改")
        self.assertEqual(self.data_manager.retrieve('test_key'), 'count_2', "主进程数据应该被存储")
        self.assertEqual(self.state_tracker.get_current_state(), 'manual_config_test', "主进程状态应该被修改")
        
        print("✓ 手动配置代理功能测试通过")
    
    def test_decorator_configuration(self):
        """测试装饰器配置功能"""
        print("\n=== 测试装饰器配置功能 ===")
        
        @with_proxy_objects({
            'counter': self.counter,
            'data': self.data_manager,
            'tracker': self.state_tracker
        })
        def decorated_worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
            """装饰器配置的工作函数"""
            process_id = os.getpid()
            print(f"装饰器配置工作进程 {process_id} 开始执行")
            
            counter = proxy_accessor.get_proxy('counter')
            data = proxy_accessor.get_proxy('data')
            tracker = proxy_accessor.get_proxy('tracker')
            
            # 执行操作
            results = []
            for i in range(3):
                count = counter.increment()
                data.store(f'item_{i}', count)
                tracker.change_state(f'decorator_step_{i}', process_id)
                results.append(count)
            
            print(f"装饰器配置工作进程 {process_id} 执行完成")
            
            return {
                'process_id': process_id,
                'final_count': counter.get_value(),
                'data_keys': data.list_keys(),
                'stats': data.get_stats(),
                'results': results
            }
        
        # 使用extract_proxy_config提取配置
        enabled, config, objects = extract_proxy_config(decorated_worker)
        
        # 验证提取的配置
        self.assertTrue(enabled, "装饰器应该启用代理功能")
        self.assertEqual(set(objects.keys()), {'counter', 'data', 'tracker'}, "应该提取所有装饰器对象")
        
        print(f"提取的配置: enabled={enabled}, objects={list(objects.keys())}")
        
        # 创建进程
        process = EnhancedProcess(
            target=decorated_worker,
            enable_proxy=enabled,
            proxy_config=config,
            proxy_objects=objects
        )
        
        # 执行测试
        process.start()
        result = process.get_result(timeout=15)
        
        # 验证结果
        self.assertIsNotNone(result, "应该返回结果")
        self.assertEqual(result['final_count'], 3, "最终计数应该是3")
        self.assertEqual(len(result['data_keys']), 3, "应该有3个数据键")
        self.assertEqual(result['stats']['data_count'], 3, "数据统计应该正确")
        self.assertEqual(result['results'], [1, 2, 3], "递增结果应该正确")
        
        # 验证主进程状态
        self.assertEqual(self.counter.get_value(), 3, "主进程计数器应该是3")
        self.assertEqual(len(self.data_manager.list_keys()), 3, "主进程应该有3个数据项")
        
        print("✓ 装饰器配置功能测试通过")
    
    def test_function_signature_detection(self):
        """测试函数签名自动检测"""
        print("\n=== 测试函数签名自动检测 ===")
        
        # 支持proxy_accessor的函数
        def proxy_enabled_worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
            """支持代理的工作函数"""
            process_id = os.getpid()
            print(f"代理支持工作进程 {process_id} 开始执行")
            
            counter = proxy_accessor.get_proxy('counter')
            result = counter.increment()
            
            print(f"代理支持工作进程 {process_id} 执行完成，结果: {result}")
            return {'process_id': process_id, 'result': result, 'proxy_used': True}
        
        # 不支持proxy_accessor的函数
        def legacy_worker(shared_data_proxy, data_queue, process_logger):
            """传统工作函数（不支持代理）"""
            process_id = os.getpid()
            print(f"传统工作进程 {process_id} 开始执行")
            
            # 这个函数不接收proxy_accessor参数
            result = "legacy_result"
            
            print(f"传统工作进程 {process_id} 执行完成")
            return {'process_id': process_id, 'result': result, 'proxy_used': False}
        
        # 测试支持代理的函数
        print("测试支持代理的函数...")
        process1 = EnhancedProcess(
            target=proxy_enabled_worker,
            enable_proxy=True,
            proxy_objects={'counter': self.counter}
        )
        
        process1.start()
        result1 = process1.get_result(timeout=15)
        
        self.assertIsNotNone(result1, "代理支持函数应该返回结果")
        self.assertEqual(result1['result'], 1, "代理调用应该成功")
        self.assertTrue(result1['proxy_used'], "应该使用代理")
        self.assertEqual(self.counter.get_value(), 1, "计数器应该被修改")
        
        # 重置计数器
        self.counter.reset()
        
        # 测试不支持代理的函数（应该正常运行但不传递proxy_accessor）
        print("测试不支持代理的函数...")
        process2 = EnhancedProcess(
            target=legacy_worker,
            enable_proxy=True,  # 即使启用代理，函数也应该正常运行
            proxy_objects={'counter': self.counter}
        )
        
        process2.start()
        result2 = process2.get_result(timeout=15)
        
        self.assertIsNotNone(result2, "传统函数应该返回结果")
        self.assertEqual(result2['result'], "legacy_result", "传统函数应该正常执行")
        self.assertFalse(result2['proxy_used'], "应该不使用代理")
        self.assertEqual(self.counter.get_value(), 0, "计数器不应该被修改")
        
        print("✓ 函数签名自动检测测试通过")
    
    def test_context_manager_usage(self):
        """测试上下文管理器使用"""
        print("\n=== 测试上下文管理器使用 ===")
        
        def worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
            """上下文管理器测试工作函数"""
            process_id = os.getpid()
            print(f"上下文管理器工作进程 {process_id} 开始执行")
            
            available = proxy_accessor.list_available_objects()
            print(f"可用对象: {available}")
            
            counter = proxy_accessor.get_proxy('counter')
            data = proxy_accessor.get_proxy('data')
            tracker = proxy_accessor.get_proxy('tracker')
            
            count = counter.increment()
            data.store('context_test', count)
            tracker.change_state('context_test_state', process_id)
            
            print(f"上下文管理器工作进程 {process_id} 执行完成")
            
            return {
                'process_id': process_id,
                'available_objects': available,
                'count': count,
                'stored_value': data.retrieve('context_test'),
                'final_state': tracker.get_current_state()
            }
        
        # 使用上下文管理器
        with ProcessProxyContext() as ctx:
            ctx.register('counter', self.counter)
            ctx.register('data', self.data_manager)
            ctx.register('tracker', self.state_tracker)
            
            # 验证上下文管理器状态
            objects = ctx.get_objects()
            self.assertEqual(set(objects.keys()), {'counter', 'data', 'tracker'}, "上下文应该包含所有注册对象")
            
            process = EnhancedProcess(
                target=worker,
                enable_proxy=True,
                proxy_objects=objects
            )
        
        # 执行测试
        process.start()
        result = process.get_result(timeout=15)
        
        # 验证结果
        self.assertIsNotNone(result, "应该返回结果")
        self.assertEqual(set(result['available_objects']), {'counter', 'data', 'tracker'}, "应该列出所有可用对象")
        self.assertEqual(result['count'], 1, "计数应该正确")
        self.assertEqual(result['stored_value'], 1, "存储值应该正确")
        self.assertEqual(result['final_state'], 'context_test_state', "状态应该正确")
        
        # 验证主进程状态
        self.assertEqual(self.counter.get_value(), 1, "主进程计数器应该被修改")
        self.assertEqual(self.data_manager.retrieve('context_test'), 1, "主进程数据应该被存储")
        self.assertEqual(self.state_tracker.get_current_state(), 'context_test_state', "主进程状态应该被修改")
        
        print("✓ 上下文管理器使用测试通过")
    
    def test_proxy_object_management(self):
        """测试代理对象管理功能"""
        print("\n=== 测试代理对象管理功能 ===")
        
        # 创建进程但不预注册对象
        process = EnhancedProcess(
            target=lambda: None,  # 空函数，我们只测试管理功能
            enable_proxy=True
        )
        
        # 测试动态注册对象
        process.register_proxy_object('counter', self.counter)
        process.register_proxy_object('data', self.data_manager)
        
        # 验证注册状态
        registered = process.get_registered_proxy_objects()
        self.assertEqual(set(registered), {'counter', 'data'}, "应该注册指定对象")
        
        # 测试重复注册（应该失败）
        with self.assertRaises(ValueError):
            process.register_proxy_object('counter', self.counter)
        
        # 测试注销对象
        process.unregister_proxy_object('data')
        registered_after_unregister = process.get_registered_proxy_objects()
        self.assertEqual(set(registered_after_unregister), {'counter'}, "应该只剩下counter")
        
        # 测试注销不存在的对象（应该失败）
        with self.assertRaises(KeyError):
            process.unregister_proxy_object('nonexistent')
        
        print("✓ 代理对象管理功能测试通过")


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
