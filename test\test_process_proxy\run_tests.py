#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProcessProxy测试运行脚本
=======================

该脚本提供完整的ProcessProxy功能测试执行和报告生成功能。

功能特性:
- 运行所有测试模块或指定模块
- 生成详细的测试报告
- 性能基准记录和分析
- 测试结果统计和可视化

使用方法:
    python run_tests.py                    # 运行所有测试
    python run_tests.py --module basic     # 运行基础功能测试
    python run_tests.py --verbose          # 详细输出
    python run_tests.py --report           # 生成测试报告

作者：Augment Agent
版本：1.0.0
"""

import unittest
import sys
import os
import time
import argparse
from io import StringIO

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入测试模块
from test_basic_functionality import TestProcessProxyCore
from test_enhanced_process_integration import TestEnhancedProcessIntegration
from test_error_handling import TestErrorHandling
from test_performance import TestPerformance


class TestRunner:
    """测试运行器"""
    
    def __init__(self, verbose=False, generate_report=False):
        """
        初始化测试运行器
        
        Args:
            verbose (bool): 是否详细输出
            generate_report (bool): 是否生成报告
        """
        self.verbose = verbose
        self.generate_report = generate_report
        self.test_results = {}
        self.start_time = None
        self.end_time = None
    
    def run_all_tests(self):
        """运行所有测试"""
        print("=" * 80)
        print("ProcessProxy功能测试套件")
        print("=" * 80)
        print(f"开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        self.start_time = time.time()
        
        # 定义测试模块
        test_modules = [
            ('基础功能测试', TestProcessProxyCore),
            ('EnhancedProcess集成测试', TestEnhancedProcessIntegration),
            ('异常处理测试', TestErrorHandling),
            ('性能测试', TestPerformance)
        ]
        
        total_tests = 0
        total_failures = 0
        total_errors = 0
        
        for module_name, test_class in test_modules:
            print(f"\n{'='*60}")
            print(f"运行 {module_name}")
            print(f"{'='*60}")
            
            # 运行测试模块
            result = self._run_test_module(test_class)
            
            # 记录结果
            self.test_results[module_name] = result
            total_tests += result['tests_run']
            total_failures += result['failures']
            total_errors += result['errors']
            
            # 显示模块结果
            self._print_module_result(module_name, result)
        
        self.end_time = time.time()
        
        # 显示总结
        self._print_summary(total_tests, total_failures, total_errors)
        
        # 生成报告
        if self.generate_report:
            self._generate_report()
        
        return total_failures == 0 and total_errors == 0
    
    def run_specific_module(self, module_name):
        """运行指定测试模块"""
        module_map = {
            'basic': ('基础功能测试', TestProcessProxyCore),
            'integration': ('EnhancedProcess集成测试', TestEnhancedProcessIntegration),
            'error': ('异常处理测试', TestErrorHandling),
            'performance': ('性能测试', TestPerformance)
        }
        
        if module_name not in module_map:
            print(f"错误: 未知的测试模块 '{module_name}'")
            print(f"可用模块: {', '.join(module_map.keys())}")
            return False
        
        print("=" * 80)
        print(f"ProcessProxy {module_map[module_name][0]}")
        print("=" * 80)
        print(f"开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        self.start_time = time.time()
        
        # 运行指定模块
        display_name, test_class = module_map[module_name]
        result = self._run_test_module(test_class)
        self.test_results[display_name] = result
        
        self.end_time = time.time()
        
        # 显示结果
        self._print_module_result(display_name, result)
        self._print_summary(result['tests_run'], result['failures'], result['errors'])
        
        if self.generate_report:
            self._generate_report()
        
        return result['failures'] == 0 and result['errors'] == 0
    
    def _run_test_module(self, test_class):
        """运行单个测试模块"""
        # 创建测试套件
        suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
        
        # 创建测试运行器
        stream = StringIO()
        runner = unittest.TextTestRunner(
            stream=stream,
            verbosity=2 if self.verbose else 1
        )
        
        # 运行测试
        start_time = time.time()
        result = runner.run(suite)
        end_time = time.time()
        
        # 获取输出
        output = stream.getvalue()
        if self.verbose:
            print(output)
        
        return {
            'tests_run': result.testsRun,
            'failures': len(result.failures),
            'errors': len(result.errors),
            'execution_time': end_time - start_time,
            'output': output,
            'failure_details': result.failures,
            'error_details': result.errors
        }
    
    def _print_module_result(self, module_name, result):
        """打印模块测试结果"""
        status = "通过" if result['failures'] == 0 and result['errors'] == 0 else "失败"
        print(f"\n{module_name} 结果:")
        print(f"  状态: {status}")
        print(f"  测试数量: {result['tests_run']}")
        print(f"  失败: {result['failures']}")
        print(f"  错误: {result['errors']}")
        print(f"  执行时间: {result['execution_time']:.2f}秒")
        
        # 显示失败和错误详情
        if result['failures']:
            print(f"\n失败详情:")
            for test, traceback in result['failure_details']:
                print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")
        
        if result['errors']:
            print(f"\n错误详情:")
            for test, traceback in result['error_details']:
                print(f"  - {test}: {traceback.split('Exception:')[-1].strip()}")
    
    def _print_summary(self, total_tests, total_failures, total_errors):
        """打印测试总结"""
        execution_time = self.end_time - self.start_time
        
        print(f"\n{'='*80}")
        print("测试总结")
        print(f"{'='*80}")
        print(f"总测试数量: {total_tests}")
        print(f"总失败数量: {total_failures}")
        print(f"总错误数量: {total_errors}")
        print(f"总执行时间: {execution_time:.2f}秒")
        print(f"结束时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        if total_failures == 0 and total_errors == 0:
            print(f"\n🎉 所有测试通过！")
        else:
            print(f"\n❌ 测试失败: {total_failures + total_errors} 个问题")
        
        # 性能统计
        if total_tests > 0:
            avg_time_per_test = execution_time / total_tests
            print(f"\n性能统计:")
            print(f"  平均每测试时间: {avg_time_per_test:.3f}秒")
            print(f"  测试吞吐量: {total_tests / execution_time:.2f} 测试/秒")
    
    def _generate_report(self):
        """生成测试报告"""
        report_filename = f"test_report_{time.strftime('%Y%m%d_%H%M%S')}.txt"
        report_path = os.path.join(os.path.dirname(__file__), report_filename)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("ProcessProxy功能测试报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总执行时间: {self.end_time - self.start_time:.2f}秒\n\n")
            
            for module_name, result in self.test_results.items():
                f.write(f"{module_name}\n")
                f.write("-" * len(module_name) + "\n")
                f.write(f"测试数量: {result['tests_run']}\n")
                f.write(f"失败: {result['failures']}\n")
                f.write(f"错误: {result['errors']}\n")
                f.write(f"执行时间: {result['execution_time']:.2f}秒\n")
                f.write(f"状态: {'通过' if result['failures'] == 0 and result['errors'] == 0 else '失败'}\n\n")
                
                if result['output']:
                    f.write("详细输出:\n")
                    f.write(result['output'])
                    f.write("\n" + "="*50 + "\n\n")
        
        print(f"\n📄 测试报告已生成: {report_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='ProcessProxy功能测试运行器')
    parser.add_argument('--module', '-m', 
                       choices=['basic', 'integration', 'error', 'performance'],
                       help='运行指定的测试模块')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='详细输出')
    parser.add_argument('--report', '-r', action='store_true',
                       help='生成测试报告')
    
    args = parser.parse_args()
    
    # 创建测试运行器
    runner = TestRunner(verbose=args.verbose, generate_report=args.report)
    
    # 运行测试
    if args.module:
        success = runner.run_specific_module(args.module)
    else:
        success = runner.run_all_tests()
    
    # 返回退出码
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
