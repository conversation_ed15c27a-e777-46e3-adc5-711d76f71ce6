# -*- coding: utf-8 -*-
"""
ProcessProxy性能和稳定性测试
===========================

该模块测试ProcessProxy的性能表现和长期稳定性，
包括调用性能、内存稳定性、并发性能等。

测试类说明:
- TestPerformance: 基础性能测试
- TestStability: 稳定性测试
- TestScalability: 可扩展性测试

作者：Augment Agent
版本：1.0.0
"""

import unittest
import time
import statistics
import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from global_tools.utils.enhanced_process import EnhancedProcess
from mock_services import SimpleCounter, DataManager, StateTracker
from test_utils import TestUtils, PerformanceMonitor


class TestPerformance(unittest.TestCase):
    """基础性能测试"""
    
    def setUp(self):
        """测试前准备"""
        self.counter = SimpleCounter()
        self.data_manager = DataManager()
        self.state_tracker = StateTracker()
        self.performance_monitor = PerformanceMonitor()
    
    def tearDown(self):
        """测试后清理"""
        self.counter.reset()
        self.data_manager.clear_all()
        self.state_tracker.reset_state()
    
    def test_call_performance(self):
        """测试代理调用性能"""
        print("\n=== 测试代理调用性能 ===")
        
        def performance_worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
            """性能测试工作函数"""
            process_id = os.getpid()
            print(f"性能测试进程 {process_id} 开始执行")
            
            counter = proxy_accessor.get_proxy('counter')
            
            # 执行大量调用并测量时间
            call_count = 100
            start_time = time.time()
            
            for i in range(call_count):
                counter.increment()
            
            end_time = time.time()
            total_time = end_time - start_time
            calls_per_second = call_count / total_time
            
            print(f"性能测试进程 {process_id} 执行完成")
            print(f"执行 {call_count} 次调用，耗时 {total_time:.3f}秒")
            print(f"性能: {calls_per_second:.2f} 调用/秒")
            
            return {
                'process_id': process_id,
                'call_count': call_count,
                'total_time': total_time,
                'calls_per_second': calls_per_second,
                'final_count': counter.get_value(),
                'avg_call_time': total_time / call_count
            }
        
        process = EnhancedProcess(
            target=performance_worker,
            enable_proxy=True,
            proxy_objects={'counter': self.counter}
        )
        
        process.start()
        result = process.get_result(timeout=30)
        
        # 验证结果
        self.assertIsNotNone(result, "应该返回性能测试结果")
        self.assertEqual(result['final_count'], 100, "应该执行100次调用")
        self.assertEqual(self.counter.get_value(), 100, "主进程计数器应该是100")
        
        # 性能要求验证
        self.assertGreater(result['calls_per_second'], 10, "性能应该至少10次调用/秒")
        self.assertLess(result['avg_call_time'], 0.1, "平均调用时间应该小于0.1秒")
        
        print(f"✓ 代理调用性能测试通过")
        print(f"  性能指标: {result['calls_per_second']:.2f} 调用/秒")
        print(f"  平均调用时间: {result['avg_call_time']:.4f} 秒")
    
    def test_memory_stability(self):
        """测试内存稳定性"""
        print("\n=== 测试内存稳定性 ===")
        
        def memory_test_worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
            """内存稳定性测试工作函数"""
            process_id = os.getpid()
            print(f"内存测试进程 {process_id} 开始执行")
            
            data_mgr = proxy_accessor.get_proxy('data')
            counter = proxy_accessor.get_proxy('counter')
            
            # 大量数据操作
            operation_count = 1000
            start_time = time.time()
            
            for i in range(operation_count):
                # 存储数据
                data_mgr.store(f'key_{i}', f'value_{i}' * 10)  # 较大的值
                counter.increment()
                
                # 定期清理部分数据以测试内存管理
                if i > 0 and i % 100 == 0:
                    # 清理一些旧数据
                    for j in range(max(0, i-50), i-25):
                        try:
                            data_mgr.delete(f'key_{j}')
                        except:
                            pass  # 忽略删除失败
                
                # 定期检查状态
                if i % 200 == 0:
                    current_stats = data_mgr.get_stats()
                    print(f"进度 {i}/{operation_count}: 数据条目 {current_stats['data_count']}")
            
            end_time = time.time()
            total_time = end_time - start_time
            
            final_stats = data_mgr.get_stats()
            final_count = counter.get_value()
            
            print(f"内存测试进程 {process_id} 执行完成")
            print(f"执行 {operation_count} 次操作，耗时 {total_time:.2f}秒")
            
            return {
                'process_id': process_id,
                'operation_count': operation_count,
                'total_time': total_time,
                'operations_per_second': operation_count / total_time,
                'final_stats': final_stats,
                'final_count': final_count,
                'memory_efficiency': final_stats['data_count'] / operation_count
            }
        
        process = EnhancedProcess(
            target=memory_test_worker,
            enable_proxy=True,
            proxy_objects={
                'data': self.data_manager,
                'counter': self.counter
            }
        )
        
        process.start()
        result = process.get_result(timeout=60)
        
        # 验证结果
        self.assertIsNotNone(result, "应该返回内存测试结果")
        self.assertEqual(result['final_count'], 1000, "应该执行1000次计数操作")
        self.assertGreaterEqual(result['final_stats']['total_operations'], 1000, "应该有至少1000次数据操作")
        
        # 内存效率验证（由于有删除操作，数据条目应该少于总操作数）
        self.assertLess(result['memory_efficiency'], 1.0, "内存效率应该小于1.0（有删除操作）")
        self.assertGreater(result['memory_efficiency'], 0.5, "内存效率应该大于0.5")
        
        # 性能验证
        self.assertGreater(result['operations_per_second'], 50, "应该至少50次操作/秒")
        
        print(f"✓ 内存稳定性测试通过")
        print(f"  操作性能: {result['operations_per_second']:.2f} 操作/秒")
        print(f"  内存效率: {result['memory_efficiency']:.2f}")
        print(f"  最终数据条目: {result['final_stats']['data_count']}")
    
    def test_concurrent_access_performance(self):
        """测试并发访问性能"""
        print("\n=== 测试并发访问性能 ===")
        
        def concurrent_worker(shared_data_proxy, data_queue, process_logger, proxy_accessor, worker_id):
            """并发性能测试工作函数"""
            process_id = os.getpid()
            print(f"并发测试进程 {worker_id} (PID: {process_id}) 开始执行")
            
            counter = proxy_accessor.get_proxy('counter')
            data_mgr = proxy_accessor.get_proxy('data')
            tracker = proxy_accessor.get_proxy('tracker')
            
            operations_per_worker = 50
            start_time = time.time()
            
            for i in range(operations_per_worker):
                # 执行多种操作
                count = counter.increment()
                data_mgr.store(f'worker_{worker_id}_item_{i}', count)
                tracker.change_state(f'worker_{worker_id}_step_{i}', process_id)
                
                # 偶尔读取数据
                if i % 10 == 0:
                    data_mgr.retrieve(f'worker_{worker_id}_item_{max(0, i-5)}')
            
            end_time = time.time()
            execution_time = end_time - start_time
            total_operations = operations_per_worker * 3  # increment + store + change_state
            operations_per_second = total_operations / execution_time
            
            print(f"并发测试进程 {worker_id} 执行完成")
            
            return {
                'worker_id': worker_id,
                'process_id': process_id,
                'execution_time': execution_time,
                'operations_per_second': operations_per_second,
                'total_operations': total_operations,
                'final_counter': counter.get_value()
            }
        
        # 创建多个并发进程
        num_processes = 4
        processes = []
        for i in range(num_processes):
            process = EnhancedProcess(
                target=concurrent_worker,
                enable_proxy=True,
                proxy_objects={
                    'counter': self.counter,
                    'data': self.data_manager,
                    'tracker': self.state_tracker
                }
            )
            processes.append(process)
        
        # 同时启动所有进程
        overall_start_time = time.time()
        for i, process in enumerate(processes):
            process.start(worker_id=i)
        
        # 收集结果
        results = []
        for i, process in enumerate(processes):
            result = process.get_result(timeout=30)
            self.assertIsNotNone(result, f"进程 {i} 应该返回结果")
            results.append(result)
        
        overall_execution_time = time.time() - overall_start_time
        
        # 验证结果
        expected_total_increments = num_processes * 50  # 4个进程 × 50次操作
        final_count = self.counter.get_value()
        final_data_count = len(self.data_manager.list_keys())
        
        self.assertEqual(final_count, expected_total_increments, "计数器应该达到预期值")
        self.assertEqual(final_data_count, expected_total_increments, "数据条目数应该正确")
        
        # 性能分析
        individual_ops_per_sec = [r['operations_per_second'] for r in results]
        avg_ops_per_sec = statistics.mean(individual_ops_per_sec)
        total_operations = sum(r['total_operations'] for r in results)
        overall_ops_per_sec = total_operations / overall_execution_time
        
        # 并发效率分析
        max_individual_time = max(r['execution_time'] for r in results)
        concurrency_efficiency = max_individual_time / overall_execution_time
        
        print(f"✓ 并发访问性能测试通过")
        print(f"  总执行时间: {overall_execution_time:.2f}秒")
        print(f"  平均个体性能: {avg_ops_per_sec:.2f} 操作/秒")
        print(f"  整体性能: {overall_ops_per_sec:.2f} 操作/秒")
        print(f"  并发效率: {concurrency_efficiency:.2f}")
        print(f"  最终状态: 计数={final_count}, 数据={final_data_count}")
        
        # 性能要求验证
        self.assertGreater(avg_ops_per_sec, 20, "平均性能应该至少20操作/秒")
        self.assertGreater(overall_ops_per_sec, 50, "整体性能应该至少50操作/秒")
        self.assertLess(overall_execution_time, 30, "总执行时间应该小于30秒")
        self.assertGreater(concurrency_efficiency, 0.8, "并发效率应该大于0.8")
    
    def test_scalability_analysis(self):
        """测试可扩展性分析"""
        print("\n=== 测试可扩展性分析 ===")
        
        def scalability_worker(shared_data_proxy, data_queue, process_logger, proxy_accessor, operations_count):
            """可扩展性测试工作函数"""
            process_id = os.getpid()
            
            counter = proxy_accessor.get_proxy('counter')
            
            start_time = time.time()
            for i in range(operations_count):
                counter.increment()
            end_time = time.time()
            
            execution_time = end_time - start_time
            ops_per_second = operations_count / execution_time if execution_time > 0 else 0
            
            return {
                'process_id': process_id,
                'operations_count': operations_count,
                'execution_time': execution_time,
                'ops_per_second': ops_per_second
            }
        
        # 测试不同操作数量的性能
        test_sizes = [10, 50, 100, 200]
        scalability_results = []
        
        for size in test_sizes:
            print(f"测试 {size} 次操作的性能...")
            
            # 重置计数器
            self.counter.reset()
            
            process = EnhancedProcess(
                target=scalability_worker,
                enable_proxy=True,
                proxy_objects={'counter': self.counter}
            )
            
            process.start(operations_count=size)
            result = process.get_result(timeout=30)
            
            self.assertIsNotNone(result, f"大小 {size} 应该返回结果")
            self.assertEqual(self.counter.get_value(), size, f"计数器应该是 {size}")
            
            scalability_results.append({
                'size': size,
                'execution_time': result['execution_time'],
                'ops_per_second': result['ops_per_second']
            })
            
            print(f"  {size} 次操作: {result['execution_time']:.3f}秒, {result['ops_per_second']:.2f} 操作/秒")
        
        # 分析可扩展性
        print("\n可扩展性分析:")
        for i, result in enumerate(scalability_results):
            if i > 0:
                prev_result = scalability_results[i-1]
                size_ratio = result['size'] / prev_result['size']
                time_ratio = result['execution_time'] / prev_result['execution_time']
                efficiency_ratio = time_ratio / size_ratio
                
                print(f"  {prev_result['size']} -> {result['size']}: "
                      f"时间比例 {time_ratio:.2f}, 效率比例 {efficiency_ratio:.2f}")
                
                # 效率比例应该接近1.0（线性扩展）
                self.assertLess(efficiency_ratio, 2.0, "效率比例应该小于2.0")
        
        # 验证性能不会严重下降
        min_ops_per_sec = min(r['ops_per_second'] for r in scalability_results)
        max_ops_per_sec = max(r['ops_per_second'] for r in scalability_results)
        performance_variance = (max_ops_per_sec - min_ops_per_sec) / max_ops_per_sec
        
        print(f"  性能方差: {performance_variance:.2f}")
        self.assertLess(performance_variance, 0.5, "性能方差应该小于50%")
        
        print("✓ 可扩展性分析测试通过")


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
