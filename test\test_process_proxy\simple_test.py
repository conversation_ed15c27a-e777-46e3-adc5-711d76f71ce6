#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的ProcessProxy测试
====================

用于验证ProcessProxy基础功能是否正常工作。
"""

import os
import sys
import time
import multiprocessing

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from global_tools.utils.enhanced_process import EnhancedProcess
from mock_services import Simple<PERSON>ounter


def simple_worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
    """简单的工作函数"""
    process_id = os.getpid()
    print(f"子进程 {process_id} 开始执行")

    # 检查 proxy_accessor 是否可用
    if proxy_accessor is None:
        print(f"子进程 {process_id}: proxy_accessor 为 None!")
        with shared_data_proxy.get_lock():
            shared_data_proxy.set('error', 'proxy_accessor is None')
        return

    # 列出可用的代理对象
    try:
        available = proxy_accessor.list_available_objects()
        print(f"子进程 {process_id}: 可用代理对象: {available}")
    except Exception as e:
        print(f"子进程 {process_id}: 列出代理对象失败: {e}")
        with shared_data_proxy.get_lock():
            shared_data_proxy.set('error', f'list_available_objects failed: {e}')
        return

    # 获取代理对象
    try:
        print(f"子进程 {process_id}: 开始获取代理对象...")
        counter = proxy_accessor.get_proxy('counter')
        print(f"子进程 {process_id}: 获取到计数器代理: {counter}")
        print(f"子进程 {process_id}: 代理对象类型: {type(counter)}")

        # 检查代理对象的属性
        if counter is not None:
            print(f"子进程 {process_id}: 代理对象有 increment 方法: {hasattr(counter, 'increment')}")
            if hasattr(counter, '_ProcessProxy__obj_id'):
                print(f"子进程 {process_id}: 代理对象ID: {counter._ProcessProxy__obj_id}")
            if hasattr(counter, '_ProcessProxy__manager_proxy'):
                print(f"子进程 {process_id}: 管理器代理: {counter._ProcessProxy__manager_proxy}")

    except Exception as e:
        print(f"子进程 {process_id}: 获取代理对象失败: {e}")
        import traceback
        print(f"子进程 {process_id}: 错误堆栈: {traceback.format_exc()}")
        with shared_data_proxy.get_lock():
            shared_data_proxy.set('error', f'get_proxy failed: {e}')
        return

    # 执行操作
    try:
        result1 = counter.increment()
        print(f"子进程 {process_id}: 第一次increment结果: {result1}")

        result2 = counter.increment()
        print(f"子进程 {process_id}: 第二次increment结果: {result2}")

        print(f"子进程 {process_id} 执行完成: {result1}, {result2}")

        # 通过共享数据返回结果
        with shared_data_proxy.get_lock():
            shared_data_proxy.set('result1', result1)
            shared_data_proxy.set('result2', result2)
            shared_data_proxy.set('process_id', process_id)
            shared_data_proxy.set('success', True)

    except Exception as e:
        print(f"子进程 {process_id}: 执行操作失败: {e}")
        with shared_data_proxy.get_lock():
            shared_data_proxy.set('error', f'operation failed: {e}')
            shared_data_proxy.set('success', False)


def main():
    """主测试函数"""
    print("开始简单的ProcessProxy测试")
    
    # 创建测试服务
    counter = SimpleCounter()
    print(f"初始计数: {counter.get_value()}")
    
    # 验证代理功能是否启用
    print("创建进程...")
    process = EnhancedProcess(
        target=simple_worker,
        enable_proxy=True,
        proxy_objects={'counter': counter}
    )

    # 检查代理功能状态
    print(f"代理功能已启用: {process.is_proxy_enabled()}")
    print(f"已注册对象: {process.get_registered_proxy_objects()}")

    # 启动进程
    process.start()

    # 启动后检查代理管理器
    proxy_manager = process.get_proxy_manager()
    print(f"启动后代理管理器: {proxy_manager}")
    if proxy_manager:
        print(f"管理器已注册对象: {proxy_manager.get_registered_objects()}")
        print(f"管理器地址: {proxy_manager.address}")
        print(f"管理器是否为服务端: {proxy_manager._ProcessProxyManager__is_server}")
    
    # 等待完成
    success = process.wait_for_completion(timeout=10)
    print(f"进程完成状态: {success}")
    
    # 获取共享数据
    shared_data = EnhancedProcess.get_shared_data_proxy()
    with shared_data.get_lock():
        result1 = shared_data.get('result1')
        result2 = shared_data.get('result2')
        process_id = shared_data.get('process_id')
        success = shared_data.get('success')
        error = shared_data.get('error')

    print(f"从共享数据获取的结果: result1={result1}, result2={result2}, process_id={process_id}")
    print(f"成功状态: {success}, 错误信息: {error}")

    # 验证主进程中的计数器状态
    final_count = counter.get_value()
    print(f"最终计数: {final_count}")

    # 检查是否有错误
    if error:
        print(f"✗ 测试失败！错误: {error}")
        return False

    # 验证结果
    if final_count == 2 and result1 == 1 and result2 == 2 and success:
        print("✓ 测试通过！ProcessProxy正常工作")
        return True
    else:
        print("✗ 测试失败！")
        print(f"  期望: final_count=2, result1=1, result2=2, success=True")
        print(f"  实际: final_count={final_count}, result1={result1}, result2={result2}, success={success}")
        return False


if __name__ == '__main__':
    multiprocessing.freeze_support()
    success = main()
    sys.exit(0 if success else 1)
