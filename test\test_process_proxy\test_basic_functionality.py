# -*- coding: utf-8 -*-
"""
ProcessProxy基础功能测试
=======================

该模块测试ProcessProxy的核心功能，包括代理创建、跨进程方法调用、
状态同步和多进程并发访问等基础功能。

测试类说明:
- TestProcessProxyCore: ProcessProxy核心功能测试
- TestCrossProcessCalls: 跨进程调用真实性测试
- TestConcurrentAccess: 并发访问一致性测试

作者：Augment Agent
版本：1.0.0
"""

import unittest
import time
import os
import multiprocessing
import sys

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from global_tools.utils.enhanced_process import (
    EnhancedProcess, ProcessProxyManager, ProcessProxy, 
    ProxyAccessor, create_proxy_manager, create_proxy
)
from mock_services import SimpleCounter, DataManager, StateTracker
from test_utils import TestUtils, PerformanceMonitor


# 模块级别的工作函数，避免pickle问题
def cross_process_worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
    """跨进程测试工作函数"""
    process_id = os.getpid()
    print(f"子进程 {process_id} 开始执行")

    # 获取代理对象
    counter = proxy_accessor.get_proxy('counter')
    tracker = proxy_accessor.get_proxy('tracker')
    data_mgr = proxy_accessor.get_proxy('data')

    # 执行操作
    result1 = counter.increment()
    result2 = counter.increment()
    state_change = tracker.change_state(f"process_{process_id}", process_id)
    data_mgr.store(f"proc_{process_id}", f"data_from_{process_id}")

    print(f"子进程 {process_id} 执行完成: counter={result2}, state={state_change}")

    return {
        'process_id': process_id,
        'counter_results': [result1, result2],
        'state_change': state_change,
        'final_counter': counter.get_value(),
        'stored_data': data_mgr.retrieve(f"proc_{process_id}")
    }


def concurrent_worker(shared_data_proxy, data_queue, process_logger, proxy_accessor, worker_id):
    """多进程并发测试工作函数"""
    process_id = os.getpid()
    print(f"工作进程 {worker_id} (PID: {process_id}) 开始执行")

    counter = proxy_accessor.get_proxy('counter')
    data_mgr = proxy_accessor.get_proxy('data')
    tracker = proxy_accessor.get_proxy('tracker')

    # 每个进程执行多次操作
    results = []
    for i in range(3):
        count = counter.increment()
        data_mgr.store(f"worker_{worker_id}_item_{i}", f"value_{count}")
        tracker.change_state(f"worker_{worker_id}_step_{i}", process_id)
        results.append(count)
        time.sleep(0.05)  # 短暂延迟，增加并发冲突可能性

    print(f"工作进程 {worker_id} 完成，结果: {results}")

    return {
        'worker_id': worker_id,
        'process_id': process_id,
        'results': results,
        'data_keys': data_mgr.list_keys(),
        'stats': data_mgr.get_stats()
    }


class TestProcessProxyCore(unittest.TestCase):
    """ProcessProxy核心功能测试"""
    
    def setUp(self):
        """测试前准备"""
        self.counter = SimpleCounter()
        self.data_manager = DataManager()
        self.state_tracker = StateTracker()
        self.test_utils = TestUtils()
        self.performance_monitor = PerformanceMonitor()
    
    def tearDown(self):
        """测试后清理"""
        # 重置所有服务状态
        self.counter.reset()
        self.data_manager.clear_all()
        self.state_tracker.reset_state()
    
    def test_basic_proxy_creation(self):
        """测试基础代理创建功能"""
        print("\n=== 测试基础代理创建功能 ===")
        
        # 创建代理管理器
        manager = create_proxy_manager()
        manager.start()
        
        try:
            # 注册对象
            manager.register_object('counter', self.counter)
            manager.register_object('data', self.data_manager)
            manager.register_object('tracker', self.state_tracker)
            
            # 验证注册状态
            registered = manager.get_registered_objects()
            self.assertIn('counter', registered)
            self.assertIn('data', registered)
            self.assertIn('tracker', registered)
            print(f"已注册对象: {registered}")
            
            # 创建代理对象
            counter_proxy = create_proxy(manager, 'counter')
            data_proxy = create_proxy(manager, 'data')
            tracker_proxy = create_proxy(manager, 'tracker')
            
            # 验证代理对象可用性
            self.assertIsNotNone(counter_proxy)
            self.assertIsNotNone(data_proxy)
            self.assertIsNotNone(tracker_proxy)
            print("代理对象创建成功")
            
        finally:
            manager.shutdown()
    
    def test_real_cross_process_calls(self):
        """测试真实的跨进程方法调用"""
        print("\n=== 测试真实的跨进程方法调用 ===")
        
        # 记录初始状态
        initial_count = self.counter.get_value()
        initial_state = self.state_tracker.get_current_state()
        initial_data_count = len(self.data_manager.list_keys())
        
        print(f"初始状态: count={initial_count}, state={initial_state}, data_count={initial_data_count}")
        
        # 创建EnhancedProcess并启用代理
        process = EnhancedProcess(
            target=cross_process_worker,
            enable_proxy=True,
            proxy_objects={
                'counter': self.counter,
                'tracker': self.state_tracker,
                'data': self.data_manager
            }
        )
        
        # 启动进程
        process.start()

        # 等待进程完成
        success = process.wait_for_completion(timeout=15)
        self.assertTrue(success, "进程应该在超时时间内完成")

        # 获取结果
        result = process.get_result()

        # 验证结果
        self.assertIsNotNone(result, "子进程应该返回结果")

        # 检查结果格式
        print(f"获取到的结果: {result}")
        print(f"结果类型: {type(result)}")

        # 如果结果是字典格式，验证内容
        if isinstance(result, dict):
            self.assertEqual(result['counter_results'], [1, 2], "计数器结果应该是[1, 2]")
            self.assertEqual(result['final_counter'], 2, "最终计数应该是2")
        
        # 验证主进程对象状态确实被修改（真实调用验证）
        final_count = self.counter.get_value()
        final_state = self.state_tracker.get_current_state()
        final_data_count = len(self.data_manager.list_keys())
        
        print(f"最终状态: count={final_count}, state={final_state}, data_count={final_data_count}")
        
        # 关键验证：确保是真实的跨进程调用
        self.assertEqual(final_count, 2, "主进程中的计数器应该被真实修改")
        self.assertNotEqual(final_state, initial_state, "主进程中的状态应该被真实修改")
        self.assertEqual(final_data_count, initial_data_count + 1, "主进程中的数据应该被真实添加")
        
        # 验证调用历史
        history = self.counter.get_call_history()
        self.assertTrue(len(history) >= 2, "应该有至少2次调用记录")
        
        # 验证进程统计
        process_stats = self.state_tracker.get_process_stats()
        self.assertEqual(len(process_stats), 1, "应该有1个进程的调用记录")
        
        print("✓ 真实跨进程调用验证通过")
    
    def test_multiple_processes_consistency(self):
        """测试多进程并发访问的一致性"""
        print("\n=== 测试多进程并发访问的一致性 ===")
        
        # 记录初始状态
        initial_count = self.counter.get_value()
        initial_data_count = len(self.data_manager.list_keys())
        
        print(f"初始状态: count={initial_count}, data_count={initial_data_count}")
        
        # 创建多个进程
        num_processes = 3
        processes = []
        for i in range(num_processes):
            process = EnhancedProcess(
                target=concurrent_worker,
                enable_proxy=True,
                proxy_objects={
                    'counter': self.counter,
                    'data': self.data_manager,
                    'tracker': self.state_tracker
                }
            )
            processes.append(process)
        
        # 启动所有进程
        start_time = time.time()
        for i, process in enumerate(processes):
            process.start(worker_id=i)
        
        # 收集结果
        results = []
        for i, process in enumerate(processes):
            success = process.wait_for_completion(timeout=20)
            self.assertTrue(success, f"进程 {i} 应该在超时时间内完成")

            result = process.get_result()
            self.assertIsNotNone(result, f"进程 {i} 应该返回结果")
            results.append(result)
        
        execution_time = time.time() - start_time
        print(f"所有进程执行完成，总耗时: {execution_time:.2f}秒")
        
        # 验证一致性
        expected_total_increments = num_processes * 3  # 3个进程 × 3次操作
        final_count = self.counter.get_value()
        final_data_count = len(self.data_manager.list_keys())
        
        print(f"最终状态: count={final_count}, data_count={final_data_count}")
        print(f"预期: count={expected_total_increments}, data_count={initial_data_count + expected_total_increments}")
        
        # 1. 计数器应该达到预期值
        self.assertEqual(final_count, expected_total_increments, 
                        f"计数器应该达到{expected_total_increments}（{num_processes}个进程 × 3次操作）")
        
        # 2. 数据管理器应该有正确数量的条目
        expected_data_count = initial_data_count + expected_total_increments
        self.assertEqual(final_data_count, expected_data_count,
                        f"数据管理器应该有{expected_data_count}个条目")
        
        # 3. 状态追踪器应该记录所有进程的调用
        process_stats = self.state_tracker.get_process_stats()
        unique_processes = len(process_stats)
        self.assertEqual(unique_processes, num_processes, 
                        f"应该有{num_processes}个不同的进程ID")
        
        # 4. 验证所有操作都被记录
        total_state_changes = sum(process_stats.values())
        self.assertEqual(total_state_changes, expected_total_increments,
                        f"应该有{expected_total_increments}次状态变更")
        
        # 5. 验证数据完整性
        final_stats = self.data_manager.get_stats()
        self.assertGreaterEqual(final_stats['total_operations'], expected_total_increments,
                               "数据管理器操作数应该至少等于预期值")
        
        print("✓ 多进程并发一致性验证通过")
        
        # 性能分析
        avg_time_per_process = execution_time / num_processes
        operations_per_second = expected_total_increments / execution_time
        print(f"性能分析: 平均每进程 {avg_time_per_process:.2f}秒, {operations_per_second:.2f} 操作/秒")


if __name__ == '__main__':
    # Windows multiprocessing支持
    multiprocessing.freeze_support()
    # 运行测试
    unittest.main(verbosity=2)
