# -*- coding: utf-8 -*-
"""
ProcessProxy测试配置文件
=======================

该文件为pytest提供测试配置、fixtures和共享设置。

配置功能:
- 测试环境设置
- 共享fixtures
- 测试数据准备
- 清理机制

作者：Augment Agent
版本：1.0.0
"""

import pytest
import os
import sys
import time
import multiprocessing

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from .mock_services import SimpleCounter, DataManager, StateTracker
from .test_utils import TestUtils, PerformanceMonitor


@pytest.fixture(scope="function")
def simple_counter():
    """
    简单计数器fixture
    
    为每个测试函数提供一个新的SimpleCounter实例
    """
    counter = SimpleCounter()
    yield counter
    # 测试后清理
    counter.reset()


@pytest.fixture(scope="function")
def data_manager():
    """
    数据管理器fixture
    
    为每个测试函数提供一个新的DataManager实例
    """
    manager = DataManager()
    yield manager
    # 测试后清理
    manager.clear_all()


@pytest.fixture(scope="function")
def state_tracker():
    """
    状态追踪器fixture
    
    为每个测试函数提供一个新的StateTracker实例
    """
    tracker = StateTracker()
    yield tracker
    # 测试后清理
    tracker.reset_state()


@pytest.fixture(scope="function")
def test_utils():
    """
    测试工具fixture
    
    为每个测试函数提供TestUtils实例
    """
    return TestUtils()


@pytest.fixture(scope="function")
def performance_monitor():
    """
    性能监控器fixture
    
    为每个测试函数提供PerformanceMonitor实例
    """
    monitor = PerformanceMonitor()
    yield monitor
    # 测试后可以输出性能报告
    if monitor.measurements:
        print("\n性能监控报告:")
        print(monitor.create_performance_report())


@pytest.fixture(scope="function")
def proxy_objects(simple_counter, data_manager, state_tracker):
    """
    代理对象集合fixture
    
    提供完整的代理对象字典，方便测试使用
    """
    return {
        'counter': simple_counter,
        'data': data_manager,
        'tracker': state_tracker
    }


@pytest.fixture(scope="session")
def test_session_info():
    """
    测试会话信息fixture
    
    提供测试会话的基本信息
    """
    return {
        'start_time': time.time(),
        'python_version': sys.version,
        'platform': sys.platform,
        'cpu_count': multiprocessing.cpu_count(),
        'project_root': project_root
    }


@pytest.fixture(autouse=True)
def test_environment_setup():
    """
    自动测试环境设置fixture
    
    在每个测试前后自动执行环境设置和清理
    """
    # 测试前设置
    original_cwd = os.getcwd()
    
    # 确保在项目根目录执行测试
    os.chdir(project_root)
    
    yield
    
    # 测试后清理
    os.chdir(original_cwd)


def pytest_configure(config):
    """
    pytest配置钩子
    
    在测试开始前进行全局配置
    """
    # 添加自定义标记
    config.addinivalue_line(
        "markers", "slow: 标记慢速测试"
    )
    config.addinivalue_line(
        "markers", "integration: 标记集成测试"
    )
    config.addinivalue_line(
        "markers", "performance: 标记性能测试"
    )
    config.addinivalue_line(
        "markers", "error_handling: 标记错误处理测试"
    )


def pytest_collection_modifyitems(config, items):
    """
    修改测试收集项
    
    为测试项添加标记和排序
    """
    for item in items:
        # 根据测试文件名添加标记
        if "test_basic_functionality" in item.nodeid:
            item.add_marker(pytest.mark.integration)
        elif "test_enhanced_process_integration" in item.nodeid:
            item.add_marker(pytest.mark.integration)
        elif "test_error_handling" in item.nodeid:
            item.add_marker(pytest.mark.error_handling)
        elif "test_performance" in item.nodeid:
            item.add_marker(pytest.mark.performance)
            item.add_marker(pytest.mark.slow)


def pytest_runtest_setup(item):
    """
    测试运行前设置
    
    在每个测试运行前执行的设置
    """
    # 为性能测试设置特殊环境
    if "performance" in item.keywords:
        # 可以在这里设置性能测试的特殊环境
        pass


def pytest_runtest_teardown(item, nextitem):
    """
    测试运行后清理
    
    在每个测试运行后执行的清理
    """
    # 确保进程清理
    import gc
    gc.collect()


def pytest_sessionstart(session):
    """
    测试会话开始
    
    在整个测试会话开始时执行
    """
    print(f"\n开始ProcessProxy功能测试会话")
    print(f"Python版本: {sys.version}")
    print(f"平台: {sys.platform}")
    print(f"CPU核心数: {multiprocessing.cpu_count()}")
    print(f"项目根目录: {project_root}")


def pytest_sessionfinish(session, exitstatus):
    """
    测试会话结束
    
    在整个测试会话结束时执行
    """
    print(f"\nProcessProxy功能测试会话结束")
    print(f"退出状态: {exitstatus}")
    
    # 生成测试总结
    if hasattr(session, 'testscollected'):
        print(f"收集的测试数量: {session.testscollected}")
    
    if hasattr(session, 'testsfailed'):
        print(f"失败的测试数量: {session.testsfailed}")


# 自定义pytest插件
class ProcessProxyTestPlugin:
    """ProcessProxy测试插件"""
    
    def pytest_runtest_logstart(self, nodeid, location):
        """测试开始日志"""
        print(f"\n开始测试: {nodeid}")
    
    def pytest_runtest_logfinish(self, nodeid, location):
        """测试结束日志"""
        print(f"完成测试: {nodeid}")


# 注册插件
def pytest_configure(config):
    """注册自定义插件"""
    config.pluginmanager.register(ProcessProxyTestPlugin(), "process_proxy_plugin")
